# Google Maps Setup Guide

## 🗺️ **New Texas Municipal Bond Map**

I've replaced the basic SVG Texas map with a full Google Maps implementation that provides:

- ✅ **Real Google Maps** with satellite/terrain views
- ✅ **Precise location markers** for each municipality/district
- ✅ **Interactive info windows** with detailed bond information
- ✅ **Color-coded markers** based on debt, rating, or per capita debt
- ✅ **Different marker sizes** for cities vs school districts
- ✅ **Zoom and pan** functionality
- ✅ **Texas-focused view** with boundary restrictions

## 🔑 **Getting Your Google Maps API Key**

### **Step 1: Go to Google Cloud Console**
1. Visit: https://console.cloud.google.com/
2. Sign in with your Google account
3. Create a new project or select existing one

### **Step 2: Enable Maps JavaScript API**
1. Go to "APIs & Services" → "Library"
2. Search for "Maps JavaScript API"
3. Click on it and press "Enable"

### **Step 3: Create API Key**
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "API Key"
3. Copy the generated API key

### **Step 4: Restrict Your API Key (Recommended)**
1. Click on your API key to edit it
2. Under "Application restrictions":
   - Choose "HTTP referrers (web sites)"
   - Add: `http://localhost:5173/*` (for development)
   - Add: `https://yourusername.github.io/*` (for production)
3. Under "API restrictions":
   - Choose "Restrict key"
   - Select "Maps JavaScript API"
4. Save changes

## ⚙️ **Setup in Your Project**

### **Step 1: Create Environment File**
Create `client/.env.local` with your API key:
```
VITE_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

### **Step 2: Restart Development Server**
```bash
npm run dev:client
```

## 🎯 **Map Features**

### **Marker Colors:**
- **Green**: Low debt / AAA rating / Low per capita
- **Yellow**: Medium debt / A rating / Medium per capita  
- **Red**: High debt / Below A rating / High per capita
- **Gray**: No data available

### **Marker Sizes:**
- **Large circles**: School Districts
- **Small circles**: Cities/Counties

### **Interactive Features:**
- **Click markers** to see detailed info
- **Switch views** between debt, rating, and per capita
- **Zoom/pan** to explore specific regions
- **Info windows** show complete financial details

## 📍 **Location Data**

The map includes coordinates for major Texas municipalities:

### **Major Cities:**
- Houston, Dallas, Austin, San Antonio, Fort Worth, El Paso, etc.

### **Major School Districts:**
- Houston ISD, Dallas ISD, Austin ISD, Plano ISD, Katy ISD, etc.

### **Auto-Location:**
- Unknown municipalities get approximate coordinates
- Based on name matching and geographic distribution

## 🔧 **Customization Options**

You can easily modify:

### **Add More Locations:**
Edit `LOCATION_COORDINATES` in `GoogleTexasMap.tsx`:
```typescript
'Your District Name': { lat: 30.1234, lng: -97.5678 },
```

### **Change Marker Styles:**
Modify the `getMarkerColor()` function for different color schemes

### **Adjust Map Bounds:**
Update the `restriction` bounds to focus on specific regions

### **Add Custom Info:**
Extend the InfoWindow content with additional municipality data

## 💰 **API Costs**

Google Maps pricing (as of 2024):
- **First 28,000 map loads per month**: FREE
- **After that**: $7 per 1,000 loads
- **For most small projects**: Completely free

## 🚀 **Benefits Over Old Map**

1. **Precise Locations**: Real coordinates vs approximate county shapes
2. **Better UX**: Familiar Google Maps interface
3. **More Information**: Rich info windows with all bond details
4. **Scalable**: Easy to add hundreds of municipalities
5. **Mobile Friendly**: Touch-optimized controls
6. **Always Updated**: Google's latest map data

## 🔍 **Troubleshooting**

### **Map Not Loading:**
- Check API key is correct in `.env.local`
- Verify Maps JavaScript API is enabled
- Check browser console for errors

### **Markers Not Appearing:**
- Ensure municipality data is loading
- Check coordinates are valid
- Verify marker colors are not transparent

### **API Key Errors:**
- Make sure key restrictions allow your domain
- Check billing is enabled (even for free tier)
- Verify API key has Maps JavaScript API access

This gives you a professional, interactive map that's much more useful than the basic SVG version! 🎯
