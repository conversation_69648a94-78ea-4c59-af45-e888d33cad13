import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function DataTimeframeInfo() {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <i className="fas fa-info-circle text-blue-500"></i>
          Data Timeframes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline">Historical</Badge>
              <span className="text-sm font-medium">Financial Statements (1 Year Behind)</span>
            </div>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Outstanding debt amounts</li>
              <li>• Credit ratings</li>
              <li>• Municipality financial data</li>
              <li>• Texas map debt visualization</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="default">Current</Badge>
              <span className="text-sm font-medium">Bond Issuances (Your CSV Data)</span>
            </div>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Individual bond transactions</li>
              <li>• Historical issuance trends</li>
              <li>• Bond search and tracking</li>
              <li>• Issuer-specific bond details</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <i className="fas fa-lightbulb mr-2"></i>
            <strong>Note:</strong> Your CSV data represents historical bond issuances and is displayed in the 
            "Historical Bond Data" metric and trends chart. Financial statement data (debt, ratings) comes 
            from separate audit sources and represents the most recent fiscal year.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
