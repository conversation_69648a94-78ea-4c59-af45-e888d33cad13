import { useState, useEffect, useCallback } from 'react';
import { GoogleMap, LoadScript, Marker, InfoWindow } from '@react-google-maps/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Municipality } from '@shared/schema';

interface GoogleTexasMapProps {
  mapView: "debt" | "rating" | "perCapita";
  onMapViewChange: (view: "debt" | "rating" | "perCapita") => void;
  municipalities: Municipality[];
}

// Texas center coordinates
const TEXAS_CENTER = {
  lat: 31.9686,
  lng: -99.9018
};

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '400px'
};

// Approximate coordinates for major Texas cities/districts
const LOCATION_COORDINATES: { [key: string]: { lat: number; lng: number } } = {
  // Major Cities
  'Houston': { lat: 29.7604, lng: -95.3698 },
  'Dallas': { lat: 32.7767, lng: -96.7970 },
  'Austin': { lat: 30.2672, lng: -97.7431 },
  'San Antonio': { lat: 29.4241, lng: -98.4936 },
  'Fort Worth': { lat: 32.7555, lng: -97.3308 },
  'El Paso': { lat: 31.7619, lng: -106.4850 },
  'Arlington': { lat: 32.7357, lng: -97.1081 },
  'Corpus Christi': { lat: 27.8006, lng: -97.3964 },
  'Plano': { lat: 33.0198, lng: -96.6989 },
  'Lubbock': { lat: 33.5779, lng: -101.8552 },
  
  // Major School Districts (approximate locations)
  'Houston ISD': { lat: 29.7604, lng: -95.3698 },
  'Dallas ISD': { lat: 32.7767, lng: -96.7970 },
  'Austin ISD': { lat: 30.2672, lng: -97.7431 },
  'Fort Worth ISD': { lat: 32.7555, lng: -97.3308 },
  'San Antonio ISD': { lat: 29.4241, lng: -98.4936 },
  'El Paso ISD': { lat: 31.7619, lng: -106.4850 },
  'Plano ISD': { lat: 33.0198, lng: -96.6989 },
  'Garland ISD': { lat: 32.9126, lng: -96.6389 },
  'Cypress-Fairbanks ISD': { lat: 29.9668, lng: -95.6890 },
  'Klein ISD': { lat: 30.0688, lng: -95.4739 },
  'Katy ISD': { lat: 29.7858, lng: -95.8244 },
  'Frisco ISD': { lat: 33.1507, lng: -96.8236 },
  'Richardson ISD': { lat: 32.9483, lng: -96.7299 },
  'Lewisville ISD': { lat: 33.0462, lng: -97.0195 },
  'Round Rock ISD': { lat: 30.5083, lng: -97.6789 },
  'Conroe ISD': { lat: 30.3119, lng: -95.4560 },
  'Pearland ISD': { lat: 29.5638, lng: -95.2861 },
  'Alief ISD': { lat: 29.7108, lng: -95.5855 },
  'Spring ISD': { lat: 30.0799, lng: -95.4172 },
  'Humble ISD': { lat: 29.9988, lng: -95.2621 },

  // Additional Texas Cities
  'Amarillo': { lat: 35.2220, lng: -101.8313 },
  'Beaumont': { lat: 30.0803, lng: -94.1266 },
  'Brownsville': { lat: 25.9018, lng: -97.4975 },
  'College Station': { lat: 30.6280, lng: -96.3344 },
  'Denton': { lat: 33.2148, lng: -97.1331 },
  'Galveston': { lat: 29.3013, lng: -94.7977 },
  'Irving': { lat: 32.8140, lng: -96.9489 },
  'Killeen': { lat: 31.1171, lng: -97.7278 },
  'Laredo': { lat: 27.5306, lng: -99.4803 },
  'McAllen': { lat: 26.2034, lng: -98.2300 },
  'Midland': { lat: 31.9974, lng: -102.0779 },
  'Odessa': { lat: 31.8457, lng: -102.3676 },
  'Pasadena': { lat: 29.6911, lng: -95.2091 },
  'Tyler': { lat: 32.3513, lng: -95.3011 },
  'Waco': { lat: 31.5494, lng: -97.1467 },
  'Wichita Falls': { lat: 33.9137, lng: -98.4934 },

  // Additional School Districts
  'Anna ISD': { lat: 33.3487, lng: -96.5483 },
  'Aldine ISD': { lat: 29.9066, lng: -95.3816 },
  'Beaumont ISD': { lat: 30.0803, lng: -94.1266 },
  'Brownsville ISD': { lat: 25.9018, lng: -97.4975 },
  'Clear Creek ISD': { lat: 29.5583, lng: -95.0888 },
  'Galveston ISD': { lat: 29.3013, lng: -94.7977 },
  'Laredo ISD': { lat: 27.5306, lng: -99.4803 },
  'Pasadena ISD': { lat: 29.6911, lng: -95.2091 },
  'Tyler ISD': { lat: 32.3513, lng: -95.3011 }
};

export default function GoogleTexasMap({ mapView, onMapViewChange, municipalities }: GoogleTexasMapProps) {
  const [selectedMunicipality, setSelectedMunicipality] = useState<Municipality | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  // Show setup message if no API key
  if (!apiKey) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Texas Municipal Bond Map</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 flex flex-col items-center justify-center bg-muted rounded-lg">
            <i className="fas fa-map-marked-alt text-4xl text-muted-foreground mb-4"></i>
            <h3 className="text-lg font-semibold mb-2">Google Maps Setup Required</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md mb-4">
              To display the interactive Texas map, you need to set up a Google Maps API key.
            </p>
            <div className="space-y-2 text-sm">
              <p><strong>1.</strong> Get API key from Google Cloud Console</p>
              <p><strong>2.</strong> Create <code className="bg-background px-1 rounded">client/.env.local</code></p>
              <p><strong>3.</strong> Add: <code className="bg-background px-1 rounded">VITE_GOOGLE_MAPS_API_KEY=your_key</code></p>
              <p><strong>4.</strong> Restart the dev server</p>
            </div>
            <Button
              className="mt-4"
              onClick={() => window.open('https://console.cloud.google.com/google/maps-apis', '_blank')}
            >
              <i className="fas fa-external-link-alt mr-2"></i>
              Get Google Maps API Key
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get coordinates for a municipality
  const getCoordinates = (municipality: Municipality) => {
    // Try exact name match first
    if (LOCATION_COORDINATES[municipality.name]) {
      return LOCATION_COORDINATES[municipality.name];
    }
    
    // Try partial matches for ISDs
    const nameKey = Object.keys(LOCATION_COORDINATES).find(key => 
      municipality.name.toLowerCase().includes(key.toLowerCase().replace(' ISD', '')) ||
      key.toLowerCase().includes(municipality.name.toLowerCase().replace(' ISD', ''))
    );
    
    if (nameKey) {
      return LOCATION_COORDINATES[nameKey];
    }
    
    // Default to random location in Texas if not found
    return {
      lat: TEXAS_CENTER.lat + (Math.random() - 0.5) * 8,
      lng: TEXAS_CENTER.lng + (Math.random() - 0.5) * 12
    };
  };

  // Get marker color based on the current view and municipality data
  const getMarkerColor = (municipality: Municipality) => {
    if (mapView === "debt") {
      const debt = parseFloat(municipality.outstandingDebt);
      if (debt > 1_000_000_000) return '#ef4444'; // red
      if (debt > 100_000_000) return '#f59e0b'; // yellow
      return '#22c55e'; // green
    }
    
    if (mapView === "rating") {
      if (!municipality.creditRating) return '#6b7280'; // gray
      const rating = municipality.creditRating.toUpperCase();
      if (rating.startsWith("AAA")) return '#22c55e'; // green
      if (rating.startsWith("AA")) return '#3b82f6'; // blue
      if (rating.startsWith("A")) return '#f59e0b'; // yellow
      return '#ef4444'; // red
    }
    
    if (mapView === "perCapita") {
      if (!municipality.perCapitaDebt) return '#6b7280'; // gray
      const perCap = parseFloat(municipality.perCapitaDebt);
      if (perCap > 4000) return '#ef4444'; // red
      if (perCap > 2000) return '#f59e0b'; // yellow
      return '#22c55e'; // green
    }
    
    return '#3b82f6'; // default blue
  };

  // Format display value based on current view
  const getDisplayValue = (municipality: Municipality) => {
    if (mapView === "debt") {
      return `$${(parseFloat(municipality.outstandingDebt) / 1_000_000).toFixed(1)}M`;
    }
    if (mapView === "rating") {
      return municipality.creditRating || 'NR';
    }
    if (mapView === "perCapita") {
      return municipality.perCapitaDebt ? `$${parseFloat(municipality.perCapitaDebt).toLocaleString()}` : 'N/A';
    }
    return '';
  };

  const onMapLoad = useCallback(() => {
    setMapLoaded(true);
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Texas Municipal Bond Map</CardTitle>
          <div className="flex gap-2">
            <Button
              variant={mapView === "debt" ? "default" : "outline"}
              size="sm"
              onClick={() => onMapViewChange("debt")}
            >
              Outstanding Debt
            </Button>
            <Button
              variant={mapView === "rating" ? "default" : "outline"}
              size="sm"
              onClick={() => onMapViewChange("rating")}
            >
              Credit Rating
            </Button>
            <Button
              variant={mapView === "perCapita" ? "default" : "outline"}
              size="sm"
              onClick={() => onMapViewChange("perCapita")}
            >
              Per Capita Debt
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <LoadScript googleMapsApiKey={apiKey}>
          <GoogleMap
            mapContainerStyle={MAP_CONTAINER_STYLE}
            center={TEXAS_CENTER}
            zoom={6}
            onLoad={onMapLoad}
            options={{
              restriction: {
                latLngBounds: {
                  north: 36.5,
                  south: 25.8,
                  east: -93.5,
                  west: -106.6,
                },
                strictBounds: false,
              },
              mapTypeControl: false,
              streetViewControl: false,
              fullscreenControl: false,
            }}
          >
            {mapLoaded && municipalities.map((municipality) => {
              const position = getCoordinates(municipality);
              const color = getMarkerColor(municipality);
              
              return (
                <Marker
                  key={municipality.id}
                  position={position}
                  onClick={() => setSelectedMunicipality(municipality)}
                  icon={{
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: color,
                    fillOpacity: 0.8,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: municipality.type === 'school_district' ? 8 : 6,
                  }}
                />
              );
            })}
            
            {selectedMunicipality && (
              <InfoWindow
                position={getCoordinates(selectedMunicipality)}
                onCloseClick={() => setSelectedMunicipality(null)}
              >
                <div className="p-2 max-w-xs">
                  <h3 className="font-semibold text-sm">{selectedMunicipality.name}</h3>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Type:</span>
                      <Badge variant="outline" className="text-xs">
                        {selectedMunicipality.type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>County:</span>
                      <span>{selectedMunicipality.county}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Outstanding Debt:</span>
                      <span className="font-medium">
                        ${(parseFloat(selectedMunicipality.outstandingDebt) / 1_000_000).toFixed(1)}M
                      </span>
                    </div>
                    {selectedMunicipality.creditRating && (
                      <div className="flex justify-between">
                        <span>Rating:</span>
                        <Badge variant="secondary" className="text-xs">
                          {selectedMunicipality.creditRating}
                        </Badge>
                      </div>
                    )}
                    {selectedMunicipality.perCapitaDebt && (
                      <div className="flex justify-between">
                        <span>Per Capita:</span>
                        <span>${parseFloat(selectedMunicipality.perCapitaDebt).toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </InfoWindow>
            )}
          </GoogleMap>
        </LoadScript>
        
        {/* Legend */}
        <div className="mt-4 flex items-center justify-center gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span>Low/AAA</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span>Medium/A</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>High/Below A</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span>Cities</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span>School Districts</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
