#!/usr/bin/env python3
"""
Texas Bond Tracker - CSV to JSON Converter
Converts bond CSV files to properly formatted JSON for the application
"""

import pandas as pd
import json
import os
import sys
from datetime import datetime
import argparse

def clean_currency_value(value):
    """Clean currency values and convert to string numbers"""
    if pd.isna(value) or value == '':
        return "0"
    
    # Convert to string and clean
    str_value = str(value).replace('$', '').replace(',', '').replace(' ', '')
    
    # Handle parentheses for negative values
    if str_value.startswith('(') and str_value.endswith(')'):
        str_value = '-' + str_value[1:-1]
    
    try:
        # Convert to float to validate, then back to string
        float_value = float(str_value)
        return str(int(float_value)) if float_value.is_integer() else str(float_value)
    except ValueError:
        return "0"

def generate_cusip_placeholder(issuer_name, bond_name, index):
    """Generate a placeholder CUSIP-like identifier"""
    # Take first 3 letters of issuer, first 3 of bond name, plus index
    issuer_code = ''.join(c for c in issuer_name.upper() if c.isalpha())[:3].ljust(3, 'X')
    bond_code = ''.join(c for c in bond_name.upper() if c.isalpha())[:3].ljust(3, 'X')
    return f"{issuer_code}{bond_code}{index:03d}"

def determine_bond_type(pledge_info):
    """Determine bond type from pledge information"""
    if pd.isna(pledge_info) or pledge_info == '':
        return "Unknown"
    
    pledge_lower = str(pledge_info).lower()
    
    if 'go' in pledge_lower or 'general obligation' in pledge_lower:
        return "GO"
    elif 'revenue' in pledge_lower:
        return "Revenue"
    elif 'lp' in pledge_lower or 'limited pledge' in pledge_lower:
        return "Limited Pledge"
    else:
        return "Other"

def convert_csv_to_json(csv_file_path, output_type='current'):
    """
    Convert CSV file to JSON format
    
    Args:
        csv_file_path: Path to the CSV file
        output_type: 'current' for real-time issuances, 'historical' for past data
    """
    
    print(f"Reading CSV file: {csv_file_path}")
    
    try:
        # Read CSV file
        df = pd.read_csv(csv_file_path)
        print(f"Loaded {len(df)} rows from CSV")
        
        # Print column names to help with mapping
        print("CSV Columns found:")
        for i, col in enumerate(df.columns):
            print(f"  {i}: {col}")
        
        # Expected columns (flexible mapping)
        column_mapping = {
            'issuer': ['issuer', 'issuer_name', 'municipality', 'district'],
            'bond_name': ['bond_name', 'name', 'bond', 'issue_name'],
            'pledge': ['pledge', 'security', 'type', 'bond_type'],
            'principal': ['principal', 'par_amount', 'amount', 'face_value'],
            'interest': ['interest', 'interest_amount', 'coupon'],
            'total_payment': ['total_payment', 'total', 'total_amount'],
            'fiscal_year': ['fiscal_year', 'year', 'fy'],
            'id_num': ['id_num', 'id', 'bond_id', 'issue_id']
        }
        
        # Auto-detect columns
        detected_columns = {}
        for field, possible_names in column_mapping.items():
            for col in df.columns:
                if any(name.lower() in col.lower() for name in possible_names):
                    detected_columns[field] = col
                    break
        
        print("\nDetected column mapping:")
        for field, col in detected_columns.items():
            print(f"  {field}: {col}")
        
        # Convert to JSON format
        json_data = []
        
        for index, row in df.iterrows():
            # Extract data with fallbacks
            issuer_name = row.get(detected_columns.get('issuer', ''), f'Unknown Issuer {index}')
            bond_name = row.get(detected_columns.get('bond_name', ''), f'Bond Series {index}')
            pledge_info = row.get(detected_columns.get('pledge', ''), '')
            principal = clean_currency_value(row.get(detected_columns.get('principal', ''), 0))
            interest = clean_currency_value(row.get(detected_columns.get('interest', ''), 0))
            total_payment = clean_currency_value(row.get(detected_columns.get('total_payment', ''), 0))
            fiscal_year = row.get(detected_columns.get('fiscal_year', ''), datetime.now().year)
            id_num = row.get(detected_columns.get('id_num', ''), index + 1)
            
            # Create bond record
            bond_record = {
                "id": int(id_num) if str(id_num).isdigit() else index + 1,
                "cusip": generate_cusip_placeholder(str(issuer_name), str(bond_name), index),
                "issuerId": index + 1,  # You may want to map this to actual issuer IDs
                "issuerName": str(issuer_name),
                "bondName": str(bond_name),
                "parAmount": principal,
                "interestAmount": interest,
                "totalPayment": total_payment,
                "bondType": determine_bond_type(pledge_info),
                "pledge": str(pledge_info) if not pd.isna(pledge_info) else "",
                "fiscalYear": int(fiscal_year) if str(fiscal_year).isdigit() else datetime.now().year,
                "dataType": output_type
            }
            
            # Add fields specific to current vs historical
            if output_type == 'current':
                bond_record.update({
                    "issueDate": f"{fiscal_year}-07-01",  # Placeholder - you may want to add actual dates
                    "maturityDate": f"{int(fiscal_year) + 20}-07-01",  # Placeholder
                    "couponRate": "0.000",  # You may want to calculate this from interest/principal
                    "yield": "0.000",
                    "creditRating": "NR",  # Not Rated - you may want to add this data
                    "purpose": "General Purpose",  # You may want to add this data
                    "callDate": None,
                    "issuanceType": "new_money",
                    "underwriter": "TBD",
                    "bondCounsel": "TBD",
                    "saleDate": f"{fiscal_year}-06-15",
                    "deliveryDate": f"{fiscal_year}-07-01"
                })
            else:  # historical
                bond_record.update({
                    "outstandingAmount": principal,
                    "auditDate": f"{fiscal_year}-12-31"
                })
            
            json_data.append(bond_record)
        
        return json_data
        
    except Exception as e:
        print(f"Error processing CSV: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Convert bond CSV files to JSON')
    parser.add_argument('csv_file', help='Path to the CSV file')
    parser.add_argument('--type', choices=['current', 'historical'], default='current',
                       help='Type of data: current (real-time) or historical (audit data)')
    parser.add_argument('--output', help='Output JSON file path (optional)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file not found: {args.csv_file}")
        sys.exit(1)
    
    # Convert CSV to JSON
    json_data = convert_csv_to_json(args.csv_file, args.type)
    
    if json_data is None:
        print("Conversion failed!")
        sys.exit(1)
    
    # Determine output file name
    if args.output:
        output_file = args.output
    else:
        base_name = os.path.splitext(os.path.basename(args.csv_file))[0]
        output_file = f"../client/public/data/{args.type}/{base_name}.json"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Write JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Conversion complete!")
    print(f"📁 Output file: {output_file}")
    print(f"📊 Records converted: {len(json_data)}")
    print(f"🏷️  Data type: {args.type}")

if __name__ == "__main__":
    main()
